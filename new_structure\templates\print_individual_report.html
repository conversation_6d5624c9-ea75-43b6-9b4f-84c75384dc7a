<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Academic Report</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 0;
        background-color: white;
        position: relative;
      }
      /* Modern Watermark styles */
      body::before {
        content: "";
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-image: url("{{ logo_url }}");
        background-repeat: no-repeat;
        background-position: center;
        background-size: 50%;
        opacity: 0.03;
        z-index: -1;
        pointer-events: none;
        filter: grayscale(100%) contrast(0.8);
        transform: rotate(-5deg);
      }
      .report-container {
        max-width: 800px;
        margin: 0 auto;
        padding: 20px;
        position: relative;
        z-index: 1;
      }
      .header {
        text-align: center;
        margin-bottom: 15px;
        padding: 15px 0;
        background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
        border-radius: 10px;
        color: white;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 15px;
      }

      .logo-wrapper {
        flex-shrink: 0;
        order: 2;
        margin-left: auto;
        padding-right: 15px;
      }

      .header-logo {
        max-width: 60px;
        max-height: 60px;
        min-width: 30px;
        min-height: 30px;
        width: auto;
        height: auto;
        border-radius: 8px;
        border: 2px solid rgba(255, 255, 255, 0.4);
        object-fit: contain;
        background: rgba(255, 255, 255, 0.9);
        padding: 4px;
      }

      .school-info {
        flex: 1;
        order: 1;
        text-align: center;
      }

      .header h1 {
        margin: 0 0 5px 0;
        font-size: 20px;
        color: white;
        font-weight: 700;
      }
      .header p {
        margin: 3px 0;
        font-size: 13px;
        color: rgba(255, 255, 255, 0.95);
      }
      .student-details {
        background: linear-gradient(
          135deg,
          rgba(76, 175, 80, 0.1) 0%,
          rgba(139, 195, 74, 0.1) 100%
        );
        border-radius: 8px;
        padding: 10px;
        margin-bottom: 10px;
        border: 1px solid rgba(76, 175, 80, 0.2);
      }

      .student-details p {
        margin: 3px 0;
        font-size: 12px;
        font-weight: 500;
        color: #2e7d32;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 10px;
        border-radius: 8px;
        overflow: hidden;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
      }
      th,
      td {
        border: 1px solid #ddd;
        padding: 5px 4px;
        text-align: center;
        font-size: 11px;
      }
      th {
        background-color: #4caf50;
        color: white;
        font-weight: bold;
        font-size: 11px;
      }
      tr:nth-child(even) {
        background-color: #f9f9f9;
      }
      .remarks {
        margin-top: 10px;
        background: linear-gradient(
          135deg,
          rgba(33, 150, 243, 0.05) 0%,
          rgba(30, 136, 229, 0.05) 100%
        );
        border-radius: 8px;
        padding: 10px;
        border: 1px solid rgba(33, 150, 243, 0.1);
      }
      .remarks h3 {
        font-size: 12px;
        color: #1976d2;
        margin-bottom: 5px;
        font-weight: 600;
      }
      .remarks p {
        margin: 3px 0;
        font-size: 11px;
        line-height: 1.3;
        color: #424242;
      }
      .footer {
        text-align: center;
        margin-top: 10px;
        padding: 8px;
        background: linear-gradient(
          135deg,
          rgba(96, 125, 139, 0.1) 0%,
          rgba(69, 90, 100, 0.1) 100%
        );
        border-radius: 8px;
        border: 1px solid rgba(96, 125, 139, 0.2);
      }

      .footer p {
        margin: 2px 0;
        font-size: 10px;
        color: #546e7a;
        font-weight: 500;
      }
      .logo-container img {
        max-width: 120px;
        height: auto;
      }

      /* Modern Performance indicators styling */
      .performance-indicators {
        margin: 25px 0;
        padding: 20px;
        background-color: #f8f9fa;
        border-radius: 10px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
        border: none;
      }
      .performance-indicators h3 {
        margin-top: 0;
        font-size: 18px;
        color: #333;
        margin-bottom: 20px;
        font-weight: 600;
        position: relative;
        padding-bottom: 8px;
      }
      .performance-indicators h3:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 0;
        width: 50px;
        height: 3px;
        background: linear-gradient(to right, #4caf50, #8bc34a);
        border-radius: 3px;
      }
      .performance-chart {
        display: flex;
        align-items: center;
        margin-bottom: 15px;
        position: relative;
      }
      .chart-label {
        width: 150px;
        font-size: 14px;
        font-weight: 500;
        color: #444;
      }
      .chart-bar {
        flex-grow: 1;
        height: 28px;
        background-color: #edf2f7;
        border-radius: 6px;
        overflow: hidden;
        position: relative;
        box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      .chart-fill {
        height: 100%;
        background: linear-gradient(to right, #4caf50, #8bc34a);
        position: absolute;
        left: 0;
        top: 0;
        border-radius: 6px;
        transition: width 0.5s ease;
      }
      /* Performance level indicators */
      .chart-fill.excellent {
        background: linear-gradient(to right, #4caf50, #8bc34a);
      }
      .chart-fill.good {
        background: linear-gradient(to right, #8bc34a, #cddc39);
      }
      .chart-fill.average {
        background: linear-gradient(to right, #ffeb3b, #ffc107);
      }
      .chart-fill.below-average {
        background: linear-gradient(to right, #ff9800, #ff5722);
      }
      .chart-value {
        width: 60px;
        text-align: right;
        padding-left: 15px;
        font-weight: 600;
        font-size: 15px;
        color: #333;
      }
      .chart-average {
        position: absolute;
        height: 100%;
        width: 2px;
        background-color: #f44336;
        top: 0;
        z-index: 2;
      }
      .chart-average:after {
        content: "";
        position: absolute;
        top: -5px;
        left: -4px;
        width: 10px;
        height: 10px;
        background-color: #f44336;
        border-radius: 50%;
      }
      .chart-legend {
        display: flex;
        justify-content: flex-end;
        margin-top: 15px;
        font-size: 12px;
      }
      .legend-item {
        display: flex;
        align-items: center;
        margin-left: 15px;
        background-color: #fff;
        padding: 5px 10px;
        border-radius: 20px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
      }
      .legend-color {
        width: 12px;
        height: 12px;
        margin-right: 5px;
        border-radius: 50%;
      }
      .legend-student {
        background: linear-gradient(to right, #4caf50, #8bc34a);
      }
      .legend-average {
        background-color: #f44336;
      }
      .performance-summary {
        background-color: #fff;
        border-radius: 8px;
        padding: 12px 15px;
        margin-top: 15px;
        box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
        font-size: 14px;
        color: #555;
        display: flex;
        justify-content: space-between;
      }
      .summary-item {
        display: flex;
        flex-direction: column;
        align-items: center;
      }
      .summary-value {
        font-size: 18px;
        font-weight: 600;
        color: #333;
        margin-bottom: 5px;
      }
      .summary-label {
        font-size: 12px;
        color: #666;
      }

      /* Enhanced print styles for single-page optimization */
      @media print {
        @page {
          size: A4 portrait;
          margin: 0.5cm;
        }

        html,
        body {
          margin: 0 !important;
          padding: 0 !important;
          height: auto !important;
          font-size: 9pt !important;
        }

        .report-container {
          max-width: none !important;
          margin: 0 !important;
          padding: 8px !important;
          background: white !important;
          box-shadow: none !important;
          border: none !important;
          border-radius: 0 !important;
          page-break-inside: avoid;
        }

        .header {
          margin-bottom: 8px !important;
          padding: 8px 0 !important;
          page-break-inside: avoid;
        }

        .header h1 {
          font-size: 16pt !important;
          margin: 0 0 3px 0 !important;
        }

        .header p {
          font-size: 9pt !important;
          margin: 1px 0 !important;
        }

        .student-details {
          padding: 6px !important;
          margin-bottom: 6px !important;
          page-break-inside: avoid;
        }

        .student-details p {
          font-size: 9pt !important;
          margin: 1px 0 !important;
        }

        table {
          margin-bottom: 6px !important;
          font-size: 8pt !important;
          page-break-inside: avoid;
        }

        th,
        td {
          padding: 3px 2px !important;
          font-size: 8pt !important;
        }

        .remarks {
          margin-top: 6px !important;
          padding: 6px !important;
          page-break-inside: avoid;
        }

        .remarks h3 {
          font-size: 10pt !important;
          margin-bottom: 3px !important;
        }

        .remarks p {
          font-size: 8pt !important;
          margin: 2px 0 !important;
          line-height: 1.1 !important;
        }

        .footer {
          margin-top: 6px !important;
          padding: 4px !important;
          page-break-inside: avoid;
        }

        .footer p {
          font-size: 7pt !important;
          margin: 1px 0 !important;
        }

        /* Ensure no page breaks within critical sections */
        .header,
        .student-details,
        table,
        .remarks,
        .footer {
          page-break-inside: avoid;
        }
      }
    </style>
  </head>
  <body onload="setTimeout(function() { window.print(); }, 500)">
    <script>
      // Hide URL and page title when printing
      document.title = "Academic Report";

      // Add event listener for after print
      window.addEventListener("afterprint", function () {
        // You can add code here if needed after printing
      });
    </script>
    <div class="report-container">
      <div class="header">
        <!-- School Logo Section -->
        <div class="logo-wrapper">
          {% if school_info.logo_filename %}
          <img
            src="{{ url_for('static', filename='uploads/logos/' + school_info.logo_filename) }}"
            alt="{{ school_info.school_name|default('School') }} Logo"
            class="header-logo"
            id="schoolLogo"
            onerror="showLogoFallback(this)"
            onload="adjustLogoStyle(this)"
          />
          {% else %}
          <!-- Use the known Hillview logo file -->
          <img
            src="{{ url_for('static', filename='uploads/logos/optimized_school_logo_1750595986_hvs.jpg') }}"
            alt="{{ school_info.school_name|default('School') }} Logo"
            class="header-logo"
            id="schoolLogo"
            onerror="showLogoFallback(this)"
            onload="adjustLogoStyle(this)"
          />
          {% endif %}
        </div>

        <!-- School Information Section -->
        <div class="school-info">
          <h1>{{ school_info.school_name|default('SCHOOL NAME')|upper }}</h1>
          <p>
            {{ school_info.postal_address|default('P.O. BOX 123, LOCATION') }}{%
            if school_info.school_phone %} | TEL: {{ school_info.school_phone
            }}{% endif %}
          </p>
          <p>
            {% if school_info.school_email %}Email: {{ school_info.school_email
            }}{% endif %}{% if school_info.school_website %} | Website: {{
            school_info.school_website }}{% endif %}
          </p>
          <p>
            ACADEMIC REPORT TERM {{ term.replace('_', ' ').upper() }} {{
            academic_year|default('2025') }}
          </p>
        </div>
      </div>

      <div class="student-details">
        <p>{{ student_name.upper() }} ADM NO.: {{ admission_no }}</p>
        <p>Grade {{ grade }} {{ education_level }} {{ stream }}</p>
        <p>
          Mean Points: {{ mean_points }} Total Marks: {{ total | int }} out of:
          {{ total_possible_marks }}
        </p>
        <p>Mean Mark: {{ avg_percentage | round(2) }}%</p>
        <p>Total Points: {{ total_points }}</p>
      </div>

      <table>
        <thead>
          <tr>
            <th>Subjects</th>
            <th>Entrance</th>
            <th>Mid Term</th>
            <th>End Term</th>
            <th>Avg.</th>
            <th>Subject Remarks</th>
          </tr>
        </thead>
        <tbody>
          {% for row in table_data %}
          <tr>
            <td>{{ row.subject }}</td>
            <td>{{ row.entrance | int }}</td>
            <td>{{ row.mid_term | int }}</td>
            <td>{{ row.end_term | int }}</td>
            <td>{{ row.avg | int }}</td>
            <td>{{ row.remarks | replace(' (TBD)', '') }}</td>
          </tr>
          {% endfor %}
          <tr>
            <td>Totals</td>
            <td></td>
            <td></td>
            <td>{{ total | int }}</td>
            <td>{{ total | int }}</td>
            <td></td>
          </tr>
        </tbody>
      </table>

      <!-- Modern Performance Indicators Section -->
      <div class="performance-indicators">
        <h3>Performance Analysis</h3>

        <!-- Performance Summary -->
        <div class="performance-summary">
          <div class="summary-item">
            <div class="summary-value">{{ avg_percentage | round(1) }}%</div>
            <div class="summary-label">Student Average</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">{{ class_average | round(1) }}%</div>
            <div class="summary-label">Class Average</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">{{ class_size }}</div>
            <div class="summary-label">Class Size</div>
          </div>
          <div class="summary-item">
            <div class="summary-value">{{ mean_grade }}</div>
            <div class="summary-label">Mean Grade</div>
          </div>
        </div>

        <!-- Overall Performance -->
        <div class="performance-chart" style="margin-top: 20px">
          <div class="chart-label">Overall Performance</div>
          <div class="chart-bar">
            {% if avg_percentage >= 75 %}
            <div
              class="chart-fill excellent"
              style="width: {{ avg_percentage }}%;"
            ></div>
            {% elif avg_percentage >= 50 %}
            <div
              class="chart-fill good"
              style="width: {{ avg_percentage }}%;"
            ></div>
            {% elif avg_percentage >= 30 %}
            <div
              class="chart-fill average"
              style="width: {{ avg_percentage }}%;"
            ></div>
            {% else %}
            <div
              class="chart-fill below-average"
              style="width: {{ avg_percentage }}%;"
            ></div>
            {% endif %}
            <div class="chart-average" style="left: {{ class_average }}%"></div>
          </div>
          <div class="chart-value">{{ avg_percentage | round(1) }}%</div>
        </div>

        <!-- Subject Performance -->
        {% for row in table_data %} {% set percentage = (row.avg / 100) * 100 %}
        {% set subject_avg = subject_averages.get(row.subject, 50) %} {% set
        subject_avg_percent = (subject_avg / 100) * 100 %}
        <div class="performance-chart">
          <div class="chart-label">{{ row.subject }}</div>
          <div class="chart-bar">
            {% if percentage >= 75 %}
            <div
              class="chart-fill excellent"
              style="width: {{ percentage }}%;"
            ></div>
            {% elif percentage >= 50 %}
            <div
              class="chart-fill good"
              style="width: {{ percentage }}%;"
            ></div>
            {% elif percentage >= 30 %}
            <div
              class="chart-fill average"
              style="width: {{ percentage }}%;"
            ></div>
            {% else %}
            <div
              class="chart-fill below-average"
              style="width: {{ percentage }}%;"
            ></div>
            {% endif %}
            <div
              class="chart-average"
              style="left: {{ subject_avg_percent }}%"
            ></div>
          </div>
          <div class="chart-value">{{ percentage | round(1) }}%</div>
        </div>
        {% endfor %}

        <!-- Legend -->
        <div class="chart-legend">
          <div class="legend-item">
            <div class="legend-color legend-student"></div>
            <span>Student's Score</span>
          </div>
          <div class="legend-item">
            <div class="legend-color legend-average"></div>
            <span>Class Average</span>
          </div>
        </div>
      </div>

      <div class="remarks">
        <h3>Class Teacher's Remarks:</h3>
        <p>
          {% if avg_percentage >= 80 %} Excellent work! You are exceeding
          expectations. Keep up the outstanding performance and continue to
          challenge yourself. {% elif avg_percentage >= 60 %} Good progress! You
          are meeting expectations. With continued focus and consistency, you
          have the potential to achieve even more. {% elif avg_percentage >= 40
          %} You are approaching expectations. Keep working hard and seek help
          where needed to improve your performance. {% else %} You need to put
          in more effort to meet expectations. Please seek additional support
          and practice regularly. {% endif %}
        </p>
        <p>
          Class Teacher: {{ staff_info.class_teacher_name|default('Not
          Assigned') }}
        </p>

        <h3>Head Teacher's Remarks:</h3>
        <p>
          {% if avg_percentage >= 80 %} Outstanding performance! Your dedication
          and hard work are evident. Continue to excel and be an inspiration to
          others. {% elif avg_percentage >= 60 %} Great progress! Your growing
          confidence is evident - keep practicing, and you'll excel even
          further. {% elif avg_percentage >= 40 %} There is room for
          improvement. Focus on your studies and seek guidance from your
          teachers. {% else %} Significant improvement is needed. Please work
          closely with your teachers and parents to enhance your performance. {%
          endif %}
        </p>
        <p>
          Head Teacher Name: {{ staff_info.headteacher_name|default('Head
          Teacher') }}
        </p>
        <p>Head Teacher Signature: ____________________</p>
        <p>
          Next Term Begins on: {{
          term_info.next_term_opening_date|default('TBD') }}
        </p>
      </div>

      <div class="footer">
        <p>Generated on: {{ current_date }}</p>
        <p>
          {{ school_info.school_name|default('School') }} powered by {{
          school_info.report_footer|default('Hillview SMS') }}
        </p>
      </div>
    </div>

    <script>
      // Logo handling functions
      function showLogoFallback(img) {
        const logoWrapper = img.parentElement;
        const schoolName =
          "{{ school_info.school_name|default('SCHOOL')|first }}";

        // Create fallback element
        const fallback = document.createElement("div");
        fallback.className = "logo-fallback";
        fallback.textContent = schoolName;
        fallback.style.cssText = `
          width: 60px;
          height: 60px;
          border-radius: 50%;
          background: linear-gradient(135deg, #1976d2, #42a5f5);
          color: white;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 24px;
          font-weight: bold;
          text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
          border: 3px solid rgba(255,255,255,0.4);
          box-shadow: 0 8px 25px rgba(0,0,0,0.2);
        `;

        // Replace image with fallback
        logoWrapper.replaceChild(fallback, img);
      }

      function adjustLogoStyle(img) {
        const width = img.naturalWidth;
        const height = img.naturalHeight;
        const aspectRatio = width / height;

        // Automatic size classification
        const maxDimension = Math.max(width, height);
        if (maxDimension <= 200) {
          img.classList.add("small");
        } else if (maxDimension <= 400) {
          img.classList.add("medium");
        } else {
          img.classList.add("large");
        }

        // Shape detection
        if (Math.abs(aspectRatio - 1) < 0.1) {
          img.style.borderRadius = "50%";
        } else if (aspectRatio > 1.5 || aspectRatio < 0.67) {
          img.style.borderRadius = "8px";
        }
      }
    </script>
  </body>
</html>
