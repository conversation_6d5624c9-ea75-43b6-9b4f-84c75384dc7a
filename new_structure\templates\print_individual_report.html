<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Individual Report - {{ student.name }}</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
        padding: 20px;
        color: #333;
      }

      .report-container {
        max-width: 900px;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border-radius: 20px;
        box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
        overflow: hidden;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }

      .back-btn {
        display: inline-block;
        padding: 12px 24px;
        background: linear-gradient(135deg, #667eea, #764ba2);
        color: white;
        text-decoration: none;
        border-radius: 25px;
        font-weight: 600;
        transition: all 0.3s ease;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
      }

      .back-btn:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
      }

      .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        text-align: center;
        position: relative;
        display: flex;
        justify-content: space-between;
        align-items: center;
        gap: 20px;
      }

      .header::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Ccircle cx='30' cy='30' r='4'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")
          repeat;
        opacity: 0.3;
      }

      .school-info {
        flex: 1;
        z-index: 2;
        position: relative;
      }

      .school-info h1 {
        font-size: 2.2em;
        font-weight: 700;
        margin-bottom: 10px;
        text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        letter-spacing: 1px;
      }

      .school-info p {
        font-size: 1em;
        margin-bottom: 8px;
        opacity: 0.95;
        font-weight: 500;
      }

      .logo-wrapper {
        order: 2;
        margin-left: auto;
        padding-right: 20px;
        z-index: 2;
        position: relative;
      }

      .logo-wrapper img {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        border: 3px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        object-fit: cover;
      }

      .logo-fallback {
        width: 80px;
        height: 80px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.2);
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 2em;
        font-weight: bold;
        color: white;
        border: 3px solid rgba(255, 255, 255, 0.8);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
      }

      .student-info {
        background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        color: white;
        padding: 25px 30px;
        margin: 0;
        position: relative;
        overflow: hidden;
      }

      .student-info::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='40' height='40' viewBox='0 0 40 40' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpath d='M20 20c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10zm10 0c0-5.5-4.5-10-10-10s-10 4.5-10 10 4.5 10 10 10 10-4.5 10-10z'/%3E%3C/g%3E%3C/svg%3E")
          repeat;
        opacity: 0.3;
      }

      .student-info-content {
        position: relative;
        z-index: 2;
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        align-items: center;
      }

      .student-info h2 {
        font-size: 1.8em;
        font-weight: 700;
        margin-bottom: 15px;
        text-shadow: 1px 1px 3px rgba(0, 0, 0, 0.3);
      }

      .student-info p {
        font-size: 1.1em;
        margin-bottom: 8px;
        font-weight: 500;
        opacity: 0.95;
      }

      .marks-table {
        margin: 30px;
        border-radius: 15px;
        overflow: hidden;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        background: white;
      }

      table {
        width: 100%;
        border-collapse: collapse;
        font-size: 0.95em;
      }

      thead {
        background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        color: white;
      }

      th {
        padding: 18px 12px;
        font-weight: 600;
        text-align: center;
        font-size: 0.9em;
        letter-spacing: 0.5px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.2);
        border-right: 1px solid rgba(255, 255, 255, 0.2);
      }

      th:last-child {
        border-right: none;
      }

      td {
        padding: 15px 12px;
        text-align: center;
        border-bottom: 1px solid #f0f0f0;
        border-right: 1px solid #f0f0f0;
        transition: all 0.3s ease;
      }

      td:last-child {
        border-right: none;
      }

      tbody tr {
        transition: all 0.3s ease;
      }

      tbody tr:hover {
        background: linear-gradient(135deg, #f8f9ff 0%, #e8f4fd 100%);
        transform: scale(1.01);
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      tbody tr:nth-child(even) {
        background: rgba(248, 249, 250, 0.5);
      }

      .component-row {
        background: linear-gradient(
          135deg,
          #ffeaa7 0%,
          #fab1a0 100%
        ) !important;
        font-style: italic;
      }

      .component-name {
        text-align: left !important;
        padding-left: 30px !important;
        font-size: 0.9em;
        color: #2d3436;
      }

      .component-mark,
      .component-label,
      .component-teacher {
        font-size: 0.9em;
        color: #2d3436;
      }

      .totals-row {
        background: linear-gradient(
          135deg,
          #a8edea 0%,
          #fed6e3 100%
        ) !important;
        font-weight: bold;
        font-size: 1.1em;
      }

      .remarks-section {
        margin: 30px;
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 30px;
      }

      .remarks-box {
        background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
        padding: 25px;
        border-radius: 15px;
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        position: relative;
        overflow: hidden;
      }

      .remarks-box::before {
        content: "";
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: url("data:image/svg+xml,%3Csvg width='30' height='30' viewBox='0 0 30 30' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.1'%3E%3Cpolygon points='15,0 30,15 15,30 0,15'/%3E%3C/g%3E%3C/svg%3E")
          repeat;
        opacity: 0.3;
      }

      .remarks-content {
        position: relative;
        z-index: 2;
      }

      .remarks-box h3 {
        color: #2d3436;
        font-size: 1.3em;
        margin-bottom: 15px;
        font-weight: 700;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
      }

      .remarks-box p {
        color: #2d3436;
        line-height: 1.6;
        margin-bottom: 12px;
        font-weight: 500;
      }

      .footer {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 25px 30px;
        text-align: center;
        margin-top: 0;
      }

      .footer p {
        margin-bottom: 8px;
        font-weight: 500;
        opacity: 0.95;
      }

      .no-print {
        margin-bottom: 20px;
      }

      /* Print Styles */
      @media print {
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        @page {
          size: A4 portrait;
          margin: 0.5cm;
        }

        body {
          background: white !important;
          padding: 0 !important;
          margin: 0 !important;
          font-size: 10pt !important;
        }

        .report-container {
          max-width: none !important;
          margin: 0 !important;
          padding: 0 !important;
          box-shadow: none !important;
          border-radius: 0 !important;
          background: white !important;
          page-break-inside: avoid !important;
        }

        .no-print {
          display: none !important;
        }

        .header {
          padding: 15px !important;
          page-break-inside: avoid !important;
        }

        .school-info h1 {
          font-size: 16pt !important;
          margin-bottom: 5px !important;
        }

        .school-info p {
          font-size: 9pt !important;
          margin-bottom: 3px !important;
        }

        .logo-wrapper img,
        .logo-fallback {
          width: 50px !important;
          height: 50px !important;
        }

        .student-info {
          padding: 12px 15px !important;
          page-break-inside: avoid !important;
        }

        .student-info h2 {
          font-size: 12pt !important;
          margin-bottom: 8px !important;
        }

        .student-info p {
          font-size: 9pt !important;
          margin-bottom: 4px !important;
        }

        .marks-table {
          margin: 15px !important;
          page-break-inside: avoid !important;
        }

        th {
          padding: 8px 4px !important;
          font-size: 8pt !important;
        }

        td {
          padding: 6px 4px !important;
          font-size: 8pt !important;
        }

        .component-name {
          padding-left: 15px !important;
          font-size: 7pt !important;
        }

        .component-mark,
        .component-label,
        .component-teacher {
          font-size: 7pt !important;
        }

        .remarks-section {
          margin: 15px !important;
          grid-template-columns: 1fr !important;
          gap: 15px !important;
          page-break-inside: avoid !important;
        }

        .remarks-box {
          padding: 12px !important;
          page-break-inside: avoid !important;
        }

        .remarks-box h3 {
          font-size: 10pt !important;
          margin-bottom: 8px !important;
        }

        .remarks-box p {
          font-size: 8pt !important;
          margin-bottom: 6px !important;
          line-height: 1.4 !important;
        }

        .footer {
          padding: 12px 15px !important;
          page-break-inside: avoid !important;
        }

        .footer p {
          font-size: 8pt !important;
          margin-bottom: 4px !important;
        }

        .header,
        .student-info,
        table,
        .remarks-section,
        .footer {
          page-break-inside: avoid !important;
        }

        table {
          page-break-inside: auto !important;
        }

        tr {
          page-break-inside: avoid !important;
          page-break-after: auto !important;
        }

        thead {
          display: table-header-group !important;
        }
      }
    </style>
  </head>
  <body>
    <div class="no-print">
      <a href="{{ url_for('classteacher.dashboard') }}" class="back-btn">
        ← Back to Dashboard
      </a>
    </div>

    <div class="report-container">
      <!-- Header Section -->
      <div class="header">
        <div class="school-info">
          <h1>{{ school_info.school_name or 'School Name' }}</h1>
          <p>{{ school_info.address or 'School Address' }}</p>
          <p>{{ school_info.email or '<EMAIL>' }}</p>
          <p>{{ school_info.phone or 'Phone Number' }}</p>
          <p><strong>{{ school_info.motto or 'School Motto' }}</strong></p>
        </div>
        <div class="logo-wrapper">
          {% if school_info.logo_filename %}
          <img
            src="{{ url_for('static', filename='uploads/logos/' + school_info.logo_filename) }}"
            alt="School Logo"
            onerror="this.style.display='none'; this.nextElementSibling.style.display='flex';"
          />
          <div class="logo-fallback" style="display: none">
            {{ school_info.school_name[0] if school_info.school_name else 'S' }}
          </div>
          {% else %}
          <div class="logo-fallback">
            {{ school_info.school_name[0] if school_info.school_name else 'S' }}
          </div>
          {% endif %}
        </div>
      </div>

      <!-- Student Information Section -->
      <div class="student-info">
        <div class="student-info-content">
          <div>
            <h2>{{ student.name }}</h2>
            <p><strong>Admission No:</strong> {{ admission_no }}</p>
            <p><strong>Grade:</strong> {{ grade }} {{ stream }}</p>
            <p><strong>Term:</strong> {{ term }}</p>
          </div>
          <div>
            <p>
              <strong>Assessment:</strong> {{ assessment_type.replace('_', '
              ').title() }}
            </p>
            <p><strong>Academic Year:</strong> {{ academic_year }}</p>
            <p><strong>Date:</strong> {{ current_date }}</p>
          </div>
        </div>
      </div>

      <!-- Marks Table Section -->
      <div class="marks-table">
        <table>
          <thead>
            <tr>
              <th>Subjects</th>
              {% if assessment_type.lower() == 'end_term' or
              assessment_type.lower() == 'endterm' %}
              <th>Entrance</th>
              <th>Mid Term</th>
              <th>End Term</th>
              <th>Avg.</th>
              {% else %}
              <th>{{ assessment_type.replace('_', ' ').title() }}</th>
              {% endif %}
              <th>Subject Remarks</th>
              <th>Teacher</th>
            </tr>
          </thead>
          <tbody>
            {% for row in table_data %}
            <tr>
              <td style="text-align: left; font-weight: 600">
                {{ row.subject }}
              </td>
              {% if assessment_type.lower() == 'end_term' or
              assessment_type.lower() == 'endterm' %}
              <!-- Show all assessment columns for end term -->
              <td>{{ row.entrance | int if row.entrance != 0 else '-' }}</td>
              <td>{{ row.mid_term | int if row.mid_term != 0 else '-' }}</td>
              <td>{{ row.end_term | int if row.end_term != 0 else '-' }}</td>
              <td>{{ row.avg | int if row.avg != 0 else '-' }}</td>
              {% else %}
              <!-- Show current assessment only -->
              <td>
                {{ row.current_assessment | int if row.current_assessment != 0
                else '-' }}
              </td>
              {% endif %}
              <td>{{ row.remarks | replace(' (TBD)', '') }}</td>
              <td>
                {{ subject_teachers.get(row.subject, {}).get('username', 'Not
                Assigned') if subject_teachers else 'Not Assigned' }}
              </td>
            </tr>

            <!-- Show component breakdown for composite subjects -->
            {% if composite_data and composite_data.get(row.subject) %} {% set
            components = composite_data[row.subject].components %} {% for
            component_name, component_data in components.items() %}
            <tr class="component-row">
              <td class="component-name">{{ component_name }}</td>
              {% if assessment_type.lower() == 'end_term' or
              assessment_type.lower() == 'endterm' %}
              <!-- Show component marks for all assessment columns -->
              <td class="component-mark">
                {{ component_data.mark if component_data.mark != 0 else '-' }}
              </td>
              <td class="component-mark">
                {{ component_data.mark if component_data.mark != 0 else '-' }}
              </td>
              <td class="component-mark">
                {{ component_data.mark if component_data.mark != 0 else '-' }}
              </td>
              <td class="component-mark">
                {{ component_data.mark if component_data.mark != 0 else '-' }}
              </td>
              {% else %}
              <!-- Show component mark for current assessment only -->
              <td class="component-mark">
                {{ component_data.mark if component_data.mark != 0 else '-' }}
              </td>
              {% endif %}
              <td class="component-label">
                {{ component_data.remarks | replace(' (TBD)', '') }}
              </td>
              <td class="component-teacher">
                <!-- Empty cell for component rows -->
              </td>
            </tr>
            {% endfor %} {% endif %} {% endfor %}
            <tr class="totals-row">
              <td>Totals</td>
              {% if assessment_type.lower() == 'end_term' or
              assessment_type.lower() == 'endterm' %}
              <!-- Show totals for all assessment columns -->
              <td></td>
              <td></td>
              <td>{{ total | int }}</td>
              <td>{{ total | int }}</td>
              {% else %}
              <!-- Show total for current assessment only -->
              <td>{{ total | int }}</td>
              {% endif %}
              <td></td>
              <td></td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Remarks Section -->
      <div class="remarks-section">
        <div class="remarks-box">
          <div class="remarks-content">
            <h3>Class Teacher's Remarks:</h3>
            <p>
              {% if mean_grade == 'A' %} Excellent performance! {{ student.name
              }} has demonstrated exceptional understanding and mastery of the
              curriculum. Keep up the outstanding work! {% elif mean_grade ==
              'B' %} Very good performance! {{ student.name }} shows strong
              understanding of most concepts. Continue working hard to achieve
              even better results. {% elif mean_grade == 'C' %} Good
              performance! {{ student.name }} demonstrates satisfactory
              understanding. With more effort and practice, even better results
              can be achieved. {% elif mean_grade == 'D' %} Fair performance. {{
              student.name }} needs to put in more effort and seek additional
              help in challenging areas to improve performance. {% else %} {{
              student.name }} needs significant improvement. Please seek
              additional support and dedicate more time to studies. {% endif %}
            </p>
            <p>
              <strong>Class Teacher:</strong> {{ staff_info.class_teacher or
              'TBD' }}
            </p>
          </div>
        </div>

        <div class="remarks-box">
          <div class="remarks-content">
            <h3>Head Teacher's Remarks:</h3>
            <p>
              {% if avg_percentage >= 80 %} Outstanding academic achievement! {{
              student.name }} continues to excel and set a great example for
              fellow students. {% elif avg_percentage >= 70 %} Commendable
              performance! {{ student.name }} shows consistent effort and good
              academic progress. {% elif avg_percentage >= 60 %} Satisfactory
              progress. {{ student.name }} is encouraged to maintain steady
              improvement in all subjects. {% elif avg_percentage >= 50 %} {{
              student.name }} shows potential but needs to work harder to
              achieve better results. Seek help where needed. {% else %} {{
              student.name }} requires immediate attention and additional
              support to improve academic performance. {% endif %}
            </p>
            <p>
              <strong>Head Teacher:</strong> {{ staff_info.head_teacher or 'TBD'
              }}
            </p>
          </div>
        </div>
      </div>

      <!-- Footer Section -->
      <div class="footer">
        <p>
          <strong>Next Term Opens:</strong> {{ term_info.next_term_opening_date
          }}
        </p>
        <p>
          <strong>Total Marks:</strong> {{ total }}/{{ total_possible_marks }} |
          <strong>Average:</strong> {{ avg_percentage }}% |
          <strong>Grade:</strong> {{ mean_grade }} ({{ mean_points }} points)
        </p>
        <p>
          Generated on {{ current_date }} | Academic Year {{ academic_year }}
        </p>
      </div>
    </div>
  </body>
</html>
