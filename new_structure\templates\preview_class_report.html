<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <title>Class Report</title>
    <link
      href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap"
      rel="stylesheet"
    />
    <link
      rel="stylesheet"
      href="{{ url_for('static', filename='css/style.css') }}"
    />
    <style>
      body {
        font-family: "Poppins", sans-serif;
        margin: 0;
        padding: 20px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: #333;
        min-height: 100vh;
      }
      .report-container {
        max-width: 100%;
        margin: 0 auto;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(10px);
        padding: 40px;
        box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
        border-radius: 20px;
        border: 1px solid rgba(255, 255, 255, 0.2);
      }
      .header {
        text-align: center;
        margin-bottom: 40px;
        padding: 30px 20px;
        position: relative;
        display: flex;
        flex-direction: column;
        align-items: center;
        background: linear-gradient(
          135deg,
          rgba(52, 152, 219, 0.1) 0%,
          rgba(155, 89, 182, 0.1) 100%
        );
        border-radius: 15px;
        border: 1px solid rgba(52, 152, 219, 0.2);
      }

      .header:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 90%;
        height: 3px;
        background: linear-gradient(
          90deg,
          transparent,
          #3498db,
          #9b59b6,
          #3498db,
          transparent
        );
        border-radius: 2px;
      }

      .logo-container {
        margin-bottom: 10px;
      }

      .school-logo {
        max-width: 200px;
        height: auto;
      }

      .school-info {
        text-align: center;
      }

      .header h1 {
        margin: 0;
        font-size: 32px;
        color: #2c3e50;
        font-weight: bold;
        text-transform: uppercase;
        letter-spacing: 2px;
        position: relative;
        display: inline-block;
      }

      .header h1:before,
      .header h1:after {
        content: "★";
        color: #3498db;
        font-size: 18px;
        position: relative;
        top: -5px;
        margin: 0 10px;
      }

      .header p {
        margin: 8px 0;
        font-size: 16px;
        color: #34495e;
      }
      .report-title {
        font-size: 24px;
        font-weight: bold;
        color: #2c3e50;
        margin: 15px 0 10px;
        text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.1);
        letter-spacing: 1px;
      }

      .report-subtitle {
        font-size: 16px;
        color: #34495e;
        margin: 5px 0 15px;
        background-color: #f8f9fa;
        padding: 8px 15px;
        border-radius: 5px;
        display: inline-block;
      }

      .report-detail {
        font-weight: 600;
        padding: 0 5px;
      }
      table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 40px;
        box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
        table-layout: auto;
        border-radius: 12px;
        overflow: hidden;
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
      }
      th,
      td {
        border: 1px solid rgba(52, 152, 219, 0.1);
        padding: 12px 8px;
        text-align: center;
        white-space: nowrap;
        font-size: 13px;
      }
      th {
        background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
        color: white;
        font-weight: 600;
        font-size: 12px;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
        letter-spacing: 0.5px;
      }
      tr:nth-child(even) {
        background: linear-gradient(
          135deg,
          rgba(52, 152, 219, 0.03) 0%,
          rgba(155, 89, 182, 0.03) 100%
        );
      }
      tr:hover {
        background: linear-gradient(
          135deg,
          rgba(52, 152, 219, 0.08) 0%,
          rgba(155, 89, 182, 0.08) 100%
        );
        transform: translateY(-1px);
        transition: all 0.3s ease;
      }
      .student-name {
        text-align: left;
        font-weight: 500;
      }
      .total-cell {
        font-weight: bold;
        background-color: #eaf2f8;
      }
      .avg-cell {
        font-weight: bold;
        color: #2980b9;
      }
      .grade-cell {
        font-weight: bold;
      }
      .rank-cell {
        font-weight: bold;
        background-color: #f5f5f5;
      }
      .averages-row {
        background-color: #eaf2f8;
        font-weight: bold;
      }
      .average-label {
        text-align: center;
        background-color: #3498db;
        color: white;
      }
      .subject-average,
      .total-average {
        background-color: #d6eaf8;
        color: #2980b9;
      }
      .class-average-row {
        background-color: #d4efdf;
      }
      .class-average-row .average-label {
        background-color: #27ae60;
      }
      .empty-cell {
        background-color: #f9f9f9;
      }
      .stats {
        margin-top: 40px;
        padding: 25px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.9) 0%,
          rgba(248, 249, 250, 0.9) 100%
        );
        backdrop-filter: blur(10px);
        border-radius: 15px;
        border: 1px solid rgba(52, 152, 219, 0.2);
        box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
      }
      .stats h3 {
        margin-top: 0;
        color: #2c3e50;
        font-size: 18px;
        border-bottom: 1px solid #ddd;
        padding-bottom: 10px;
      }
      .stats-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
        margin-top: 15px;
      }
      .stat-item {
        padding: 10px;
        border-radius: 5px;
      }
      .exceeding {
        background-color: #d4edda;
        color: #155724;
      }
      .meeting {
        background-color: #cce5ff;
        color: #004085;
      }
      .approaching {
        background-color: #fff3cd;
        color: #856404;
      }
      .below {
        background-color: #f8d7da;
        color: #721c24;
      }
      .footer {
        margin-top: 40px;
        text-align: center;
        font-size: 0.9em;
        color: #7f8c8d;
        border-top: 2px solid transparent;
        border-image: linear-gradient(
            90deg,
            transparent,
            #3498db,
            #9b59b6,
            #3498db,
            transparent
          )
          1;
        padding-top: 25px;
        background: linear-gradient(
          135deg,
          rgba(255, 255, 255, 0.5) 0%,
          rgba(248, 249, 250, 0.5) 100%
        );
        border-radius: 10px;
        margin-top: 30px;
        padding: 20px;
      }
      /* Subject Teachers Section */
      .subject-teachers-section {
        margin: 30px 0;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 8px;
        border: 1px solid #ddd;
        page-break-inside: avoid;
      }

      .subject-teachers-section h3 {
        margin-bottom: 15px;
        color: #333;
        font-size: 16px;
        text-align: center;
        border-bottom: 2px solid #1f7d53;
        padding-bottom: 8px;
      }

      .teachers-grid {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 15px;
        margin-top: 15px;
      }

      .teacher-info {
        background: white;
        padding: 12px;
        border-radius: 6px;
        border-left: 4px solid #1f7d53;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      }

      .teacher-info strong {
        color: #1f7d53;
        font-size: 14px;
      }

      .teacher-info small {
        color: #666;
        font-style: italic;
      }

      .signature-section {
        display: flex;
        justify-content: space-between;
        margin-top: 50px;
        margin-bottom: 30px;
      }
      .signature-box {
        width: 30%;
        text-align: center;
      }
      .signature-line {
        border-bottom: 1px solid #333;
        margin-bottom: 5px;
        height: 40px;
      }

      .teacher-name {
        font-weight: bold;
        color: #1f7d53;
        font-size: 12px;
        margin-top: 5px;
      }
      .action-buttons {
        display: flex;
        gap: 15px;
        margin-bottom: 20px;
      }
      .back-btn,
      .edit-btn,
      .delete-btn {
        display: inline-block;
        padding: 10px 20px;
        color: white;
        text-decoration: none;
        border-radius: 5px;
        transition: background-color 0.3s;
        font-weight: 500;
        border: none;
        cursor: pointer;
      }
      .back-btn {
        background-color: #3498db;
      }
      .back-btn:hover {
        background-color: #2980b9;
      }
      .edit-btn {
        background-color: #27ae60;
      }
      .edit-btn:hover {
        background-color: #219653;
      }
      .delete-btn {
        background-color: #e74c3c;
      }
      .delete-btn:hover {
        background-color: #c0392b;
      }

      /* Modal styles */
      .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
      }
      .modal-content {
        background-color: white;
        margin: 15% auto;
        padding: 20px;
        border-radius: 8px;
        width: 50%;
        max-width: 500px;
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
      }
      .modal-buttons {
        display: flex;
        justify-content: flex-end;
        margin-top: 20px;
        gap: 10px;
      }
      .cancel-btn {
        background-color: #95a5a6;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
      }
      .cancel-btn:hover {
        background-color: #7f8c8d;
      }
      .confirm-delete-btn {
        background-color: #e74c3c;
        color: white;
        border: none;
        padding: 8px 16px;
        border-radius: 4px;
        cursor: pointer;
      }
      .confirm-delete-btn:hover {
        background-color: #c0392b;
      }

      /* Print Controls Styles */
      .print-controls {
        display: flex;
        flex-wrap: wrap;
        gap: 10px;
        margin-bottom: 20px;
        background-color: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 5px solid #3498db;
        align-items: center;
      }

      .print-btn,
      .instructions-toggle {
        display: inline-flex;
        align-items: center;
        gap: 8px;
        padding: 10px 15px;
        border: none;
        border-radius: 5px;
        cursor: pointer;
        font-weight: 500;
        font-size: 14px;
        transition: all 0.3s ease;
        text-decoration: none;
        color: white;
      }

      .print-btn {
        background-color: #3498db;
      }

      .print-btn:hover {
        background-color: #2980b9;
      }

      .instructions-toggle {
        background-color: #f39c12;
        margin-left: auto;
      }

      .instructions-toggle:hover {
        background-color: #d35400;
      }

      .print-icon,
      .info-icon {
        font-style: normal;
      }

      .instructions-panel {
        position: absolute;
        background-color: white;
        border: 1px solid #ddd;
        border-radius: 8px;
        padding: 15px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        z-index: 100;
        width: 350px;
        right: 20px;
        margin-top: 10px;
      }

      .instructions-panel h4 {
        margin-top: 0;
        color: #2c3e50;
        border-bottom: 1px solid #eee;
        padding-bottom: 8px;
      }

      .instructions-panel ol {
        margin: 0;
        padding-left: 20px;
      }

      .instructions-panel li {
        margin-bottom: 8px;
      }

      .print-instructions {
        position: relative;
      }

      @media print {
        /* Remove all browser-generated content */
        @page {
          size: landscape;
          margin: 0.8cm 0.5cm;
          /* Remove headers and footers completely */
          @top-left {
            content: "";
          }
          @top-center {
            content: "";
          }
          @top-right {
            content: "";
          }
          @bottom-left {
            content: "";
          }
          @bottom-center {
            content: "";
          }
          @bottom-right {
            content: "";
          }
        }

        /* Reset all styles for clean printing */
        * {
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
          print-color-adjust: exact !important;
        }

        html {
          margin: 0 !important;
          padding: 0 !important;
        }

        body {
          background: white !important;
          padding: 0 !important;
          margin: 0 !important;
          font-size: 11pt !important;
          font-family: "Poppins", Arial, sans-serif !important;
          -webkit-print-color-adjust: exact !important;
          color-adjust: exact !important;
        }

        .report-container {
          box-shadow: none !important;
          padding: 15px !important;
          max-width: 100% !important;
          margin: 0 !important;
          background: white !important;
          border-radius: 0 !important;
          backdrop-filter: none !important;
          border: none !important;
        }

        /* Hide all non-essential elements */
        .action-buttons,
        .print-controls,
        .delete-btn,
        .modal,
        .subject-selection-form,
        .instructions-panel,
        .print-instructions {
          display: none !important;
          visibility: hidden !important;
        }

        .header {
          margin-bottom: 20px !important;
          page-break-inside: avoid !important;
          background: white !important;
          border: 1px solid #ddd !important;
          padding: 15px !important;
        }

        .school-logo {
          max-width: 120px !important;
          height: auto !important;
        }

        .stats {
          background: white !important;
          border: 1px solid #ddd !important;
          break-inside: avoid !important;
          page-break-inside: avoid !important;
          backdrop-filter: none !important;
        }

        .stat-item {
          border: 1px solid #ddd !important;
          background-color: white !important;
        }

        table {
          width: 100% !important;
          page-break-inside: auto !important;
          border-collapse: collapse !important;
          font-size: 10pt !important;
          margin-bottom: 20px !important;
        }

        th,
        td {
          padding: 6px 4px !important;
          font-size: 9pt !important;
          border: 1px solid #ddd !important;
        }

        tr {
          page-break-inside: avoid !important;
          page-break-after: auto !important;
        }

        thead {
          display: table-header-group !important;
        }

        tfoot {
          display: table-footer-group !important;
        }

        .signature-section {
          break-inside: avoid !important;
          page-break-inside: avoid !important;
          break-before: auto !important;
          margin-top: 30px !important;
        }

        .footer {
          position: static !important;
          bottom: auto !important;
          width: 100% !important;
          text-align: center !important;
          margin-top: 20px !important;
        }

        /* Ensure colors print correctly */
        th {
          background-color: #3498db !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
        }

        .averages-row {
          background-color: #eaf2f8 !important;
          -webkit-print-color-adjust: exact !important;
        }

        .average-label {
          background-color: #3498db !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
        }

        .class-average-row {
          background-color: #d4efdf !important;
          -webkit-print-color-adjust: exact !important;
        }

        .class-average-row .average-label {
          background-color: #27ae60 !important;
          color: white !important;
          -webkit-print-color-adjust: exact !important;
        }
      }
    </style>
    <script>
      function confirmDelete(grade, stream, term, assessmentType) {
        // Set up the delete form action
        const deleteForm = document.getElementById("deleteForm");
        deleteForm.action = `/classteacher/delete_marksheet/${grade}/${stream}/${term}/${assessmentType}`;

        // Update the confirmation message
        const deleteMessage = document.getElementById("deleteMessage");
        deleteMessage.textContent = `Are you sure you want to delete all marks for ${grade} ${stream} in ${term} ${assessmentType}? This action cannot be undone.`;

        // Show the modal
        const modal = document.getElementById("deleteModal");
        modal.style.display = "block";
      }

      function closeModal() {
        // Hide the modal
        const modal = document.getElementById("deleteModal");
        modal.style.display = "none";
      }

      // Close the modal if the user clicks outside of it
      window.onclick = function (event) {
        const modal = document.getElementById("deleteModal");
        if (event.target == modal) {
          modal.style.display = "none";
        }
      };

      // Function to toggle printing instructions
      function toggleInstructions() {
        const instructionsPanel = document.getElementById("instructions-panel");
        if (instructionsPanel.style.display === "none") {
          instructionsPanel.style.display = "block";
        } else {
          instructionsPanel.style.display = "none";
        }
      }

      // Close instructions panel when clicking outside of it
      document.addEventListener("click", function (event) {
        const instructionsPanel = document.getElementById("instructions-panel");
        const instructionsToggle = document.querySelector(
          ".instructions-toggle"
        );

        if (
          instructionsPanel.style.display === "block" &&
          !instructionsPanel.contains(event.target) &&
          event.target !== instructionsToggle
        ) {
          instructionsPanel.style.display = "none";
        }
      });

      // Functions for subject selection
      function selectAllSubjects() {
        const checkboxes = document.querySelectorAll(
          'input[type="checkbox"][id^="include_subject_"]'
        );
        checkboxes.forEach((checkbox) => {
          checkbox.checked = true;
        });
      }

      function deselectAllSubjects() {
        const checkboxes = document.querySelectorAll(
          'input[type="checkbox"][id^="include_subject_"]'
        );
        checkboxes.forEach((checkbox) => {
          checkbox.checked = false;
        });
      }

      function printCleanReport() {
        // Simply use the browser's built-in print functionality with clean CSS
        window.print();
      }

      function downloadReportAsPDF() {
        // Alternative method: Use the browser's built-in print to PDF
        window.print();
      }
    </script>
  </head>
  <body>
    <div class="report-container">
      <div class="action-buttons">
        <a href="{{ url_for('classteacher.dashboard') }}" class="back-btn"
          >Back to Dashboard</a
        >
        <a
          href="{{ url_for('classteacher.edit_class_marks', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
          class="edit-btn"
          >Edit Marks</a
        >
        <button
          onclick="confirmDelete('{{ grade }}', '{{ stream }}', '{{ term }}', '{{ assessment_type }}')"
          class="delete-btn"
        >
          Delete Marksheet
        </button>
      </div>

      <!-- Subject Selection Form -->
      <div
        class="subject-selection-form"
        style="
          margin-bottom: 20px;
          padding: 15px;
          background-color: #f8f9fa;
          border-radius: 8px;
          border-left: 5px solid #3498db;
        "
      >
        <h3
          style="
            margin-top: 0;
            color: #2c3e50;
            font-size: 18px;
            border-bottom: 1px solid #ddd;
            padding-bottom: 10px;
          "
        >
          Select Subjects to Include in Report
        </h3>
        <form
          method="POST"
          action="{{ url_for('classteacher.preview_class_report', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
        >
          <input type="hidden" name="csrf_token" value="{{ csrf_token() }}" />
          <div
            style="
              display: grid;
              grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
              gap: 10px;
              margin-top: 15px;
            "
          >
            {% for subject in subjects %}
            <div style="display: flex; align-items: center">
              <input
                type="checkbox"
                id="include_subject_{{ loop.index0 }}"
                name="include_subject_{{ loop.index0 }}"
                value="{{ subject.id if subject is defined and subject.id is defined else loop.index }}"
                {%
                if
                not
                session.selected_subjects
                or
                subject.id
                in
                session.selected_subjects
                %}checked{%
                endif
                %}
                style="margin-right: 8px"
              />
              <label
                for="include_subject_{{ loop.index0 }}"
                style="font-size: 14px"
                >{{ subject.name }}</label
              >
            </div>
            {% endfor %}
          </div>
          <div style="margin-top: 15px; display: flex; gap: 10px">
            <button
              type="button"
              onclick="selectAllSubjects()"
              style="
                padding: 8px 12px;
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
              "
            >
              Select All
            </button>
            <button
              type="button"
              onclick="deselectAllSubjects()"
              style="
                padding: 8px 12px;
                background-color: #95a5a6;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
              "
            >
              Deselect All
            </button>
            <button
              type="submit"
              style="
                padding: 8px 12px;
                background-color: #3498db;
                color: white;
                border: none;
                border-radius: 4px;
                cursor: pointer;
                margin-left: auto;
              "
            >
              Update Report
            </button>
          </div>
        </form>
      </div>

      <!-- Print-Friendly Controls -->
      <div class="print-controls">
        <button onclick="printCleanReport()" class="print-btn">
          <i class="print-icon">🖨️</i> Print/Download Report
        </button>
        <button
          onclick="downloadReportAsPDF()"
          class="print-btn"
          style="background-color: #28a745"
        >
          <i class="print-icon">📄</i> Quick Download
        </button>
        <a
          href="{{ url_for('classteacher.view_student_reports', grade=grade, stream=stream, term=term, assessment_type=assessment_type) }}"
          style="
            padding: 10px 15px;
            background-color: #28a745;
            color: white;
            border-radius: 5px;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
          "
        >
          <i style="font-style: normal">👨‍👩‍👧‍👦</i> View Individual Reports
        </a>
        <div class="print-instructions">
          <button onclick="toggleInstructions()" class="instructions-toggle">
            <i class="info-icon">ℹ️</i> Printing Instructions
          </button>
          <div
            id="instructions-panel"
            class="instructions-panel"
            style="display: none"
          >
            <h4>How to Print/Save This Report:</h4>
            <ol>
              <li>
                Click the <strong>Print Report</strong> button to open your
                browser's print dialog.
              </li>
              <li>
                Select your printer or choose "Save as PDF" to create a PDF
                file.
              </li>
              <li>
                For best results, use landscape orientation and enable
                background colors.
              </li>
              <li>Set margins to "Narrow" or "None" for the best layout.</li>
            </ol>
          </div>
        </div>
      </div>

      <!-- Delete Confirmation Modal -->
      <div id="deleteModal" class="modal">
        <div class="modal-content">
          <h3>Confirm Deletion</h3>
          <p id="deleteMessage">
            Are you sure you want to delete this marksheet? This action cannot
            be undone.
          </p>
          <div class="modal-buttons">
            <form id="deleteForm" method="POST" action="">
              <input
                type="hidden"
                name="csrf_token"
                value="{{ csrf_token() }}"
              />
              <button type="button" onclick="closeModal()" class="cancel-btn">
                Cancel
              </button>
              <button type="submit" class="confirm-delete-btn">Delete</button>
            </form>
          </div>
        </div>
      </div>
      <div class="header">
        <div class="logo-container">
          <img src="{{ logo_url }}" alt="School Logo" class="school-logo" />
        </div>
        <div class="school-info">
          <h1>
            {{ school_info.school_name|default('KIRIMA PRIMARY SCHOOL') }}
          </h1>
          <p>
            {{ school_info.school_address|default('P.O. BOX 123, KIRIMA') }} |
            TEL: {{ school_info.school_phone|default('+254 123 456789') }}
          </p>
          <p>
            Email: {{
            school_info.school_email|default('<EMAIL>') }} |
            Website: {{
            school_info.school_website|default('www.kirimaprimary.ac.ke') }}
          </p>
        </div>
        <div class="report-title">{{ education_level.upper() }} MARKSHEET</div>
        <p class="report-subtitle">
          <span class="report-detail"
            >GRADE: {{ grade.replace('Grade ', '') }}</span
          >
          |
          <span class="report-detail"
            >STREAM: {{ stream.replace('Stream ', '') }}</span
          >
          |
          <span class="report-detail"
            >TERM: {{ term.replace('_', ' ').upper() }}</span
          >
          |
          <span class="report-detail"
            >ASSESSMENT: {{ assessment_type.upper() }}</span
          >
        </p>
      </div>

      <!-- Table wrapper for horizontal scroll on very small screens -->
      <div style="overflow-x: auto; -webkit-overflow-scrolling: touch">
        <table>
          <thead>
            <tr>
              <th rowspan="2">S/N</th>
              <th rowspan="2">STUDENT NAME</th>
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %} {% set
              components = subject_components.get(subject_name, []) %}
              <th colspan="{{ components|length + 1 }}">
                {{ subject_name.upper() }}
              </th>
              {% else %}
              <th rowspan="2">{{ abbreviated_subjects[loop.index0] }}</th>
              {% endif %} {% endfor %}
              <th style="width: 8%" rowspan="2">TOTAL</th>
              <th style="width: 8%" rowspan="2">AVG %</th>
              <th style="width: 6%" rowspan="2">GRD</th>
              <th style="width: 6%" rowspan="2">RANK</th>
            </tr>
            <tr>
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %} {% set
              components = subject_components.get(subject_name, []) %} {% for
              component in components %}
              <th style="font-size: 11px">{{ component.name[:4].upper() }}</th>
              {% endfor %}
              <th style="font-size: 11px; background-color: #e8f4fc">TOTAL</th>
              {% endif %} {% endfor %}
            </tr>
          </thead>
          <tbody>
            {% for student_data in class_data %}
            <tr>
              <td>{{ student_data.index }}</td>
              <td class="student-name">{{ student_data.student.upper() }}</td>
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %} {% set
              components = subject_components.get(subject_name, []) %} {% set
              student_id = student_data.get('student_id', 0) %} {% for component
              in components %}
              <td style="font-size: 12px">
                {{ component_marks_data.get(student_id, {}).get(subject_name,
                {}).get(component.name, 0) | int }}
              </td>
              {% endfor %}
              <td style="background-color: #f0f8ff; font-weight: bold">
                {{ student_data.filtered_marks.get(subject_name, 0) | int }}
              </td>
              {% else %}
              <td>
                {{ student_data.filtered_marks.get(subject_name, 0) | int }}
              </td>
              {% endif %} {% endfor %}
              <td class="total-cell">
                {{ student_data.filtered_total | int }}/{{
                student_data.total_possible_marks }}
              </td>
              <td class="avg-cell">
                {{ student_data.filtered_average | round(0) }}
              </td>
              <td class="grade-cell">
                {{ student_data.performance_category }}
              </td>
              <td class="rank-cell">{{ student_data.rank }}</td>
            </tr>
            {% endfor %}

            <!-- Subject Averages Row -->
            <tr class="averages-row">
              <td colspan="2" class="average-label">SUBJECT AVERAGES</td>
              {% for subject_name in subject_names %} {% set subject_obj =
              filtered_subjects | selectattr('name', 'equalto', subject_name) |
              first %} {% if subject_obj and subject_obj.is_composite %} {% set
              components = subject_components.get(subject_name, []) %} {% for
              component in components %}
              <td class="subject-average" style="font-size: 11px">
                {{ component_averages.get(subject_name, {}).get(component.name,
                0) }}
              </td>
              {% endfor %}
              <td class="subject-average" style="background-color: #d6eaf8">
                {{ subject_averages.get(subject_name, 0) | round(2) }}
              </td>
              {% else %}
              <td class="subject-average">
                {{ subject_averages.get(subject_name, 0) | round(2) }}
              </td>
              {% endif %} {% endfor %}
              <td colspan="4" class="empty-cell"></td>
            </tr>

            <!-- Class Average Row -->
            <tr class="averages-row class-average-row">
              <td colspan="2" class="average-label">CLASS AVERAGE</td>
              <td colspan="{{ subject_names|length }}" class="empty-cell"></td>

              <!-- Class Average Total -->
              <td class="total-average">{{ class_average | round(2) }}</td>

              <!-- Class Average Percentage -->
              {% set avg_percentage_total = 0 %} {% set avg_student_count = 0 %}
              {% for student_data in class_data %} {% if
              student_data.filtered_average > 0 %} {% set avg_percentage_total =
              avg_percentage_total + student_data.filtered_average %} {% set
              avg_student_count = avg_student_count + 1 %} {% endif %} {% endfor
              %}
              <td class="avg-cell">
                {% if avg_student_count > 0 %} {{ (avg_percentage_total /
                avg_student_count) | round(2) }} {% else %} 0 {% endif %}
              </td>

              <td colspan="2" class="empty-cell"></td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Close table wrapper -->

      <div class="stats">
        <h3>Performance Summary</h3>
        <div class="stats-grid">
          <div class="stat-item exceeding">
            <strong>EE1/EE2 (Exceeding Expectation, ≥75%):</strong> {{
            stats.exceeding }} learners
          </div>
          <div class="stat-item meeting">
            <strong>ME1/ME2 (Meeting Expectation, 41-74%):</strong> {{
            stats.meeting }} learners
          </div>
          <div class="stat-item approaching">
            <strong>AE1/AE2 (Approaching Expectation, 21-40%):</strong> {{
            stats.approaching }} learners
          </div>
          <div class="stat-item below">
            <strong>BE1/BE2 (Below Expectation, <21%):</strong> {{ stats.below
            }} learners
          </div>
        </div>
      </div>

      <!-- Subject Teachers Section -->
      {% if staff_info and staff_info.subject_teachers %}
      <div class="subject-teachers-section">
        <h3>Subject Teachers</h3>
        <div class="teachers-grid">
          {% for subject, teacher in staff_info.subject_teachers.items() %}
          <div class="teacher-info">
            <strong>{{ subject }}:</strong> {{ teacher.name }} {% if
            teacher.qualification %} <br /><small
              >{{ teacher.qualification }}</small
            >
            {% endif %}
          </div>
          {% endfor %}
        </div>
      </div>
      {% endif %}

      <div class="signature-section">
        <div class="signature-box">
          <div class="signature-line"></div>
          <p>Class Teacher</p>
          {% if staff_info and staff_info.class_teacher %}
          <p class="teacher-name">{{ staff_info.class_teacher.name }}</p>
          {% endif %}
        </div>
        <div class="signature-box">
          <div class="signature-line"></div>
          <p>Deputy Head Teacher</p>
          {% if staff_info and staff_info.deputy_headteacher %}
          <p class="teacher-name">{{ staff_info.deputy_headteacher.name }}</p>
          {% endif %}
        </div>
        <div class="signature-box">
          <div class="signature-line"></div>
          <p>Head Teacher</p>
          {% if staff_info and staff_info.headteacher %}
          <p class="teacher-name">{{ staff_info.headteacher.name }}</p>
          {% endif %}
        </div>
      </div>

      <div class="footer">
        <p>Generated on: {{ current_date }}</p>
        <p>
          {{ school_info.school_name|default('School Management System') }} | {{
          school_info.report_footer|default('Powered by CbcTeachkit') }}
        </p>
      </div>
    </div>
  </body>
</html>
